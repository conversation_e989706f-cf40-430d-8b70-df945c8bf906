  
  const cors = {
	"access-control-allow-origin": "*",
	"access-control-allow-methods": "POST,OPTIONS",
	"access-control-allow-headers": "content-type,authorization",
  };
  
  const json = (obj: unknown, status = 200) =>
	new Response(JSON.stringify(obj), {
	  status,
	  headers: { "content-type": "application/json", ...cors },
	});
  
  export default {
	async fetch(req: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
	  const { pathname } = new URL(req.url);
	  if (req.method === "GET" && pathname === "/") return json({ ok: true });
	  if (req.method === "OPTIONS") return new Response(null, { headers: cors });
	  if (pathname !== "/webhook") return new Response("Not Found", { status: 404 });
	  if (req.method !== "POST") return new Response("Method Not Allowed", { status: 405 });
  
  
	  let payload: any;
	  try { payload = await req.json(); } catch { return json({ error: "Invalid JSON" }, 400); }
  
	  const pageId = payload?.data?.id ?? payload?.page_id ?? payload?.id;
	  const props  = payload?.data?.properties ?? payload?.properties ?? {};
	  console.log("webhook", { pageId, keys: Object.keys(props) });
  
	  const about = firstRichText(props["Tell us a little about yourself"]);
	  const connection = firstRichText(props["Whats your coolest n <= 3 degree connection"]);
  
	  if (env.OPENAI_API_KEY && (about)) {
		ctx.waitUntil((async () => {
		  try {
			const enhancedText = await maybeSpicifyText(about, env);
			const graphText = await maybeGenerateGraph(connection, env);
			await maybeUpdateNotion(env, pageId, enhancedText);
		  } catch (error) {
			console.error('Error in async processing:', error);
		  }
		})());
	  }

	  return json({ ok: true, pageId, received: payload });
	},
  } satisfies ExportedHandler<Env>;
  
  function firstRichText(prop: any): string | undefined {
	const arr = prop?.rich_text;
	if (Array.isArray(arr) && arr[0]?.plain_text) return String(arr[0].plain_text);
  }
  
  async function maybeSpicifyText(text: string | undefined, env: Env) {
	if (!text) return;
	const body = {
	  model: "gpt-4o-mini",
	  messages: [
		{ role: "system", content: "Make any text you receive more sassy and spicier while keeping meaning and length similar." },
		{ role: "user", content: `Rewrite: ${text}` },
	  ]
	};
	const r = await fetch("https://api.openai.com/v1/chat/completions", {
	  method: "POST",
	  headers: { "content-type": "application/json", authorization: `Bearer ${env.OPENAI_API_KEY}` },
	  body: JSON.stringify(body),
	});
	if (!r.ok) throw new Error(`OpenAI text failed: ${r.status}`);
	const j = await r.json() as any;
	const enhanced = j?.choices?.[0]?.message?.content;
	return enhanced;
  }

  async function maybeGenerateGraph(text: string | undefined, env: Env) {
	if (!text) return;
  
  async function maybeUpdateNotion(env: Env, pageId: string | undefined, enhancedText: string | undefined) {
	if (!env.NOTION_API_KEY || !pageId) {
		console.log("Missing Notion API key or page ID, skipping Notion update");
		return;
	}


	try {
		const updateData: any = {
			properties: {}
		};

		// Update enhanced text if available
		if (enhancedText) {
			updateData.properties["Tell us a little about yourself"] = {
				rich_text: [{
					type: "text",
					text: {
						content: enhancedText
					}
				}]
			};
		}

		// Only make the API call if we have properties to update
		if (Object.keys(updateData.properties).length > 0) {
			const r = await fetch(`https://api.notion.com/v1/pages/${pageId}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
					"Authorization": `Bearer ${env.NOTION_API_KEY}`,
					"Notion-Version": "2022-06-28"
				},
				body: JSON.stringify(updateData)
			});

			if (!r.ok) {
				throw new Error(`Notion API error: ${r.status} ${r.statusText}`);
			}

			console.log("Successfully updated Notion page with enhanced content");
		}
	} catch (error) {
		console.error("Error updating Notion:", error);
	}
  }
